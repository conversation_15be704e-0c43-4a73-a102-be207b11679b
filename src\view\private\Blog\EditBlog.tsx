import React, { useState, useRef, useEffect } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useNavigate, useParams } from "react-router-dom";
import { BlogService } from "../../../services/blog.service";
import toast from "react-hot-toast";


export default function EditBlog() {
  const navigate = useNavigate();
  const { handle } = useParams<{ handle: string }>();
  const coverFileInputRef = useRef<HTMLInputElement>(null);
  const writerFileInputRef = useRef<HTMLInputElement>(null);

  const [loading, setLoading] = useState(true);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [blogHandle, setBlogHandle] = useState("");
  const [isPublished, setIsPublished] = useState(false);
  const [image, setImage] = useState("");
  const [imageError, setImageError] = useState("");
  const [writerName, setWriterName] = useState("");
  const [writerShortName, setWriterShortName] = useState("");
  const [writerDesignation, setWriterDesignation] = useState("");
  const [writerImage, setWriterImage] = useState("");
  const [writerImageError, setWriterImageError] = useState("");
  const [tag, setTag] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [handleError, setHandleError] = useState("");
  const [saveError, setSaveError] = useState("");
  const [coverImageLoading, setCoverImageLoading] = useState(false);
  const [writerImageLoading, setWriterImageLoading] = useState(false);

  useEffect(() => {
    const fetchBlog = async () => {
      if (!handle) return;
      
      try {
        setLoading(true);
        const response = await BlogService.getBlogByHandle(handle);
        const blog = response.data.data;
        
        setTitle(blog.blogName || "");
        setContent(blog.description || "");
        setBlogHandle(blog.handle || "");
        setIsPublished(blog.isVisible || false);
        setImage(blog.featureImage || "");
        setWriterName(blog.writerName || "");
        setWriterShortName(blog.writerShortname || "");
        setWriterDesignation(blog.writerDesignation || "");
        setWriterImage(blog.writerImage || "");
        setTags(blog.tag || []);
      } catch (error: any) {
        toast.error("Failed to fetch blog details");
        navigate("/blogs");
      } finally {
        setLoading(false);
      }
    };

    fetchBlog();
  }, [handle, navigate]);

  const uploadImageToS3 = async (file: File) => {
    try {
      const response = await BlogService.uploadFile(file);
      return { success: true, url: response.data.data.url };
    } catch (error: any) {
      return { success: false, message: error.response?.data?.message || "Upload failed" };
    }
  };

  // Handle Image Upload
  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    setImageFunction: (url: string) => void,
    triggerBy: string
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      setImageError("File size should not exceed 5MB");
      return;
    }

    if (triggerBy === "coverImage") setCoverImageLoading(true);
    if (triggerBy === "writerImage") setWriterImageLoading(true);

    const imageUrl = await uploadImageToS3(file);
    if (imageUrl.success) {
      setImageFunction(imageUrl.url);
      setImageError("");
      setWriterImageError("");
    } else {
      if (triggerBy === "coverImage") {
        setImageError(imageUrl.message);
      } else if (triggerBy === "writerImage") {
        setWriterImageError(imageUrl.message);
      }
    }

    if (triggerBy === "coverImage") setCoverImageLoading(false);
    if (triggerBy === "writerImage") setWriterImageLoading(false);
  };

  // Handle Tag Addition
  const handleAddTag = () => {
    if (tag.trim() && !tags.includes(tag.trim())) {
      setTags([...tags, tag.trim()]);
    }
    setTag("");
    setShowDropdown(false);
  };

  // Handle Tag Removal
  const handleRemoveTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  // Handle Image Removal
  const handleRemoveImage = (setImageFunction: (url: string) => void, triggerBy: string) => {
    setImageFunction("");
    if (triggerBy === "coverImage" && coverFileInputRef.current) {
      coverFileInputRef.current.value = "";
    } else if (triggerBy === "writerImage" && writerFileInputRef.current) {
      writerFileInputRef.current.value = "";
    }
  };

  const getFormattedDate = () => {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const year = today.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Handle Form Submission
  const handleSave = async () => {
    if (!blogHandle.trim()) {
      setHandleError("Handle is required.");
      return;
    }
    setHandleError("");

    const publishDate = getFormattedDate();
    const formData = {
      blogName: title,
      description: content,
      handle: blogHandle,
      isVisible: isPublished,
      featureImage: image,
      writerName,
      writerShortname: writerShortName,
      writerDesignation: writerDesignation,
      writerImage,
      tag: tags,
      publishDate,
    };

    try {
      if (!handle) return;
      
      const response = await BlogService.updateBlog(handle, formData);

      if (response.status !== 200) {
        throw new Error("Failed to update blog post.");
      }

      toast.success("Blog post updated successfully!");
      navigate(`/blogs`);
    } catch (error: any) {
      if (error?.response?.data?.errors?.length > 0) {
        setSaveError(error?.response?.data?.errors[0]?.message);
      } else {
        setSaveError(error.message || "Failed to update blog post");
      }
      toast.error("Failed to update blog post");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-lg text-gray-500">Loading blog details...</div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto p-3 sm:p-6 space-y-6">
      {/* Page Title and Save Button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
        <h1 className="text-xl sm:text-2xl font-semibold text-left">Edit Blog Post</h1>
        <button
          onClick={handleSave}
          className="w-full sm:w-auto p-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700"
        >
          Update Post
        </button>
      </div>

      {/* Show the error if save failure */}
      {saveError && (
        <div className="bg-red-100 border border-red-400 text-red-700 p-3 rounded-md mt-2">
          <p className="text-sm">{saveError}</p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        {/* Left Section */}
        <div className="col-span-1 sm:col-span-2 space-y-6">
          {/* Title & Content Card */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-6 min-h-[500px]">
            {/* Title Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Title
              </label>
              <input
                type="text"
                placeholder="Give your blog post a title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            {/* Content Editor */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Content
              </label>
              <ReactQuill
                theme="snow"
                value={content}
                onChange={setContent}
                className="mt-1"
                style={{ height: "16em" }}
              />
            </div>
          </div>

          {/* Handle Label Card */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h2 className="text-lg font-medium text-left">Handle</h2>
            <input
              type="text"
              placeholder="Enter handle"
              value={blogHandle}
              onChange={(e) => setBlogHandle(e.target.value)}
              className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              required
            />
            {handleError && (
              <p className="text-red-500 text-sm">{handleError}</p>
            )}
          </div>
        </div>

        {/* Right Section - Same as CreateBlog but with pre-filled data */}
        <div className="space-y-6">
          {/* Visibility Toggle */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h2 className="text-lg font-medium text-left">Visibility</h2>
            <div className="flex items-center justify-between">
              <span>{isPublished ? "Visible" : "Hidden"}</span>
              <button
                onClick={() => setIsPublished(!isPublished)}
                className={`relative inline-flex items-center h-6 w-12 rounded-full transition ${
                  isPublished ? "bg-green-500" : "bg-gray-300"
                }`}
              >
                <span
                  className={`inline-block w-5 h-5 transform bg-white rounded-full transition ${
                    isPublished ? "translate-x-6" : "translate-x-1"
                  }`}
                ></span>
              </button>
            </div>
          </div>

          <button
            onClick={() => navigate("/blogs")}
            className="w-full p-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
}
