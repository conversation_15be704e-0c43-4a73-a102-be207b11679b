import makeRequest from "../api/makeRequest";

export class BlogService {
  // Get all blogs with pagination
  static async getAllBlogs(page: number = 1, limit: number = 5) {
    return await makeRequest(
      `/admin/blogs?page=${page}&limit=${limit}`,
      "get"
    );
  }

  // Get single blog by handle
  static async getBlogByHandle(handle: string) {
    return await makeRequest(`/admin/blogs/${handle}`, "get");
  }

  // Create new blog
  static async createBlog(blogData: any) {
    return await makeRequest("/admin/blogs", "post", blogData);
  }

  // Update existing blog
  static async updateBlog(handle: string, blogData: any) {
    return await makeRequest(`/admin/blogs/${handle}`, "put", blogData);
  }

  // Delete blog
  static async deleteBlog(handle: string) {
    return await makeRequest(`/admin/blogs/${handle}`, "delete");
  }

  // Upload file/image
  static async uploadFile(file: File) {
    const formData = new FormData();
    formData.append("uploaded-file", file);
    
    return await makeRequest("/admin/upload-file", "post", formData);
  }
}
